"""
FastAPI wrapper for your local Gemma-3n model.

• POST /ask         – audio→text (unchanged)
• POST /ask_image  – image+prompt→text (new)

CORS is open for http://localhost:5173 so the React front-end can call us.
"""

import base64, os, tempfile, torch
from fastapi import FastAPI, HTTPException, UploadFile, File, Form
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from gemma_record_gui import get_model_and_processor, sanitize

# --------------------------------------------------------------------
# FastAPI + CORS
# --------------------------------------------------------------------

app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173"],
    allow_methods=["POST", "OPTIONS"],
    allow_headers=["*"],
)

# --------------------------------------------------------------------
# Model loading with lazy initialization and better error handling
# --------------------------------------------------------------------

_model_cache = {"model": None, "processor": None, "loaded": False, "error": None}

def get_cached_model_and_processor():
    """Get model and processor with caching and error handling."""
    if _model_cache["loaded"] and _model_cache["error"] is None:
        return _model_cache["model"], _model_cache["processor"]

    if _model_cache["error"] is not None:
        raise HTTPException(500, f"Model loading failed: {_model_cache['error']}")

    try:
        print("Loading Gemma model... This may take a few minutes on first run.")
        model, processor = get_model_and_processor()
        _model_cache["model"] = model
        _model_cache["processor"] = processor
        _model_cache["loaded"] = True
        _model_cache["error"] = None
        print("Model loaded successfully!")
        return model, processor
    except Exception as e:
        error_msg = str(e)
        _model_cache["error"] = error_msg
        print(f"Failed to load model: {error_msg}")
        raise HTTPException(500, f"Failed to load model: {error_msg}")

@app.get("/health")
async def health_check():
    """Health check endpoint to verify server is running."""
    return {"status": "healthy", "model_loaded": _model_cache["loaded"]}

# --------------------------------------------------------------------
# /ask  — audio blob (base-64)  →  text
# --------------------------------------------------------------------

class AudioPayload(BaseModel):
    data: str                        # base-64 WAV data (no "data:…," prefix)

@app.post("/ask")
async def ask_audio(payload: AudioPayload):
    wav_path = None
    try:
        # Get model and processor with lazy loading
        model, processor = get_cached_model_and_processor()

        wav_bytes = base64.b64decode(payload.data)
        with tempfile.NamedTemporaryFile(delete=False, suffix=".wav") as tmp:
            wav_path = tmp.name
            tmp.write(wav_bytes)

        messages = [
            {
                "role": "system",
                "content": [{"type": "text", "text": "You are a friendly assistant."}],
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": "Here is my audio message:"},
                    {"type": "audio", "audio": wav_path},
                ],
            },
        ]

        inputs = processor.apply_chat_template(
            messages,
            add_generation_prompt=True,
            tokenize=True,
            return_dict=True,
            return_tensors="pt",
        ).to(model.device, dtype=model.dtype)

        with torch.inference_mode():
            out = model.generate(**inputs, max_new_tokens=256, disable_compile=True)

        reply = processor.decode(
            out[0][inputs["input_ids"].shape[-1] :], skip_special_tokens=True
        )
        return {"text": sanitize(reply)}

    except Exception as exc:
        raise HTTPException(500, str(exc)) from exc
    finally:
        if wav_path and os.path.exists(wav_path):
            os.remove(wav_path)

# --------------------------------------------------------------------
# /ask_image  — multipart(form-data)  →  text
# --------------------------------------------------------------------

@app.post("/ask_image")
async def ask_image(
    prompt: str = Form(...),
    image: UploadFile = File(...),
):
    img_path = None
    try:
        # Get model and processor with lazy loading
        model, processor = get_cached_model_and_processor()

        suffix = os.path.splitext(image.filename)[1] or ".png"
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp:
            img_path = tmp.name
            tmp.write(await image.read())

        messages = [
            {
                "role": "system",
                "content": [{"type": "text", "text": "You are a friendly assistant."}],
            },
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": img_path},
                    {"type": "text", "text": prompt},
                ],
            },
        ]

        inputs = processor.apply_chat_template(
            messages,
            add_generation_prompt=True,
            tokenize=True,
            return_dict=True,
            return_tensors="pt",
        ).to(model.device, dtype=model.dtype)

        with torch.inference_mode():
            out = model.generate(**inputs, max_new_tokens=256, disable_compile=True)

        reply = processor.decode(
            out[0][inputs["input_ids"].shape[-1] :], skip_special_tokens=True
        )
        return {"text": sanitize(reply)}

    except Exception as exc:
        raise HTTPException(500, str(exc)) from exc
    finally:
        if img_path and os.path.exists(img_path):
            os.remove(img_path)
